{"MicrosoftAppType": "MultiTenant", "MicrosoftAppId": "", "MicrosoftAppPassword": "", "MicrosoftAppTenantId": "", "BaseUrl": "https://your-app-domain.com", "ConnectionStrings": {"DefaultConnection": "User Id=postgres.msnqzbbgpsyxjmywllkb;Password=*********;Server=aws-0-us-east-1.pooler.supabase.com;Port=5432;Database=postgres", "StorageAccount": ""}, "TranscriptionService": {"ApiUrl": "", "ApiKey": "", "SpeechRegion": "", "SpeechKey": ""}, "Recording": {"StoragePath": "./recordings", "MaxRecordingDurationMinutes": 240, "SupportedFormats": ["mp4", "wav", "mp3"]}, "Teams": {"AppId": "", "AppSecret": "", "TenantId": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.Bot": "Information"}}, "AllowedHosts": "*"}