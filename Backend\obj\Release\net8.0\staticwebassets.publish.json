{"Version": 1, "Hash": "0fI1TanoCeRioQluxKeY0C8O3+v4er4JRXXhADI5PEc=", "Source": "AccureMD.TeamsBot", "BasePath": "_content/AccureMD.TeamsBot", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AccureMD.TeamsBot\\wwwroot", "Source": "AccureMD.TeamsBot", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "Pattern": "**"}], "Assets": [{"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "css/teams-app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vwl5012ydz", "Integrity": "0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\teams-app.css", "FileLength": 11424, "LastWriteTime": "2025-08-08T19:01:32+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-callback#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ere7y5064t", "Integrity": "elxfXvf7dlkoGnW2sOoZOlyOMW81PjUeeu0jeq+uqmw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-callback.html", "FileLength": 26875, "LastWriteTime": "2025-08-09T13:35:17+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-start#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ksq8v2n24n", "Integrity": "XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-start.html", "FileLength": 6131, "LastWriteTime": "2025-08-10T12:33:49+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/auth-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zbbusfory5", "Integrity": "3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\auth-test.html", "FileLength": 11310, "LastWriteTime": "2025-08-09T13:11:16+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/configure#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inynrhuf2", "Integrity": "gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\configure.html", "FileLength": 4092, "LastWriteTime": "2025-08-08T17:45:39+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h6nvgw2bxq", "Integrity": "GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\index.html", "FileLength": 6691, "LastWriteTime": "2025-08-08T20:14:05+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/privacy#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9ukuo7vfri", "Integrity": "AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\privacy.html", "FileLength": 1322, "LastWriteTime": "2025-08-08T08:30:26+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/termsofuse#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tv39flyyfq", "Integrity": "FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\termsofuse.html", "FileLength": 1168, "LastWriteTime": "2025-08-08T08:30:34+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\test-auth-fix.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "html/test-auth-fix#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ew7c11alxw", "Integrity": "AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\html\\test-auth-fix.html", "FileLength": 9642, "LastWriteTime": "2025-08-10T12:36:26+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "js/teams-app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v2ht0iv8tj", "Integrity": "SYyNHEiAHnWf6nLjhVMAGG6bB25qc3RxDo+wyenmlUs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\teams-app.js", "FileLength": 20446, "LastWriteTime": "2025-08-10T12:32:44+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "teams-test#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cztlu2xxj7", "Integrity": "+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\teams-test.html", "FileLength": 6390, "LastWriteTime": "2025-08-08T19:44:49+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2r3ito90zx", "Integrity": "fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-auth.html", "FileLength": 2140, "LastWriteTime": "2025-08-08T11:50:16+00:00"}, {"Identity": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "SourceId": "AccureMD.TeamsBot", "SourceType": "Discovered", "ContentRoot": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\", "BasePath": "_content/AccureMD.TeamsBot", "RelativePath": "test-teams-auth#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r54zswcdn2", "Integrity": "zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-teams-auth.html", "FileLength": 9705, "LastWriteTime": "2025-08-08T20:45:49+00:00"}], "Endpoints": [{"Route": "css/teams-app.css", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11424"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:01:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ="}]}, {"Route": "css/teams-app.vwl5012ydz.css", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\css\\teams-app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11424"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:01:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwl5012ydz"}, {"Name": "label", "Value": "css/teams-app.css"}, {"Name": "integrity", "Value": "sha256-0b3Vytiq3sWD55HdmkfCBjn39BgFGuxMJFo7b1oziNQ="}]}, {"Route": "html/auth-callback.ere7y5064t.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26875"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"elxfXvf7dlkoGnW2sOoZOlyOMW81PjUeeu0jeq+uqmw=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:35:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ere7y5064t"}, {"Name": "label", "Value": "html/auth-callback.html"}, {"Name": "integrity", "Value": "sha256-elxfXvf7dlkoGnW2sOoZOlyOMW81PjUeeu0jeq+uqmw="}]}, {"Route": "html/auth-callback.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-callback.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "26875"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"elxfXvf7dlkoGnW2sOoZOlyOMW81PjUeeu0jeq+uqmw=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:35:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-elxfXvf7dlkoGnW2sOoZOlyOMW81PjUeeu0jeq+uqmw="}]}, {"Route": "html/auth-start.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6131"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:33:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU="}]}, {"Route": "html/auth-start.ksq8v2n24n.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-start.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6131"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:33:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ksq8v2n24n"}, {"Name": "label", "Value": "html/auth-start.html"}, {"Name": "integrity", "Value": "sha256-XA8Du/0DmKqfwycoDHhpErxh8FCsuUbfJFOqR9bq9vU="}]}, {"Route": "html/auth-test.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11310"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:11:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k="}]}, {"Route": "html/auth-test.zbbusfory5.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\auth-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11310"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k=\""}, {"Name": "Last-Modified", "Value": "Sat, 09 Aug 2025 13:11:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zbbusfory5"}, {"Name": "label", "Value": "html/auth-test.html"}, {"Name": "integrity", "Value": "sha256-3wyDThI9iFlMwIUvzUY3zBg+1tVlmjd7W5SZW6ECH4k="}]}, {"Route": "html/configure.8inynrhuf2.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inynrhuf2"}, {"Name": "label", "Value": "html/configure.html"}, {"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}]}, {"Route": "html/configure.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\configure.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4092"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 17:45:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gP6X6Ul+2zIZ/ckSLyW7ocLHibMBGM1/IXK9o/eAHDU="}]}, {"Route": "html/index.h6nvgw2bxq.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6691"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:14:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h6nvgw2bxq"}, {"Name": "label", "Value": "html/index.html"}, {"Name": "integrity", "Value": "sha256-GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk="}]}, {"Route": "html/index.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6691"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:14:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GfcoGCssHEosZ5jXb6bdeIVquPUcwyOkuAeHCeKM3Hk="}]}, {"Route": "html/privacy.9ukuo7vfri.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ukuo7vfri"}, {"Name": "label", "Value": "html/privacy.html"}, {"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/privacy.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\privacy.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1322"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AQnTSBFMhyIS097yvdmGh79OMrkoyZqpTRb0J2gSb2s="}]}, {"Route": "html/termsofuse.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/termsofuse.tv39flyyfq.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\termsofuse.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1168"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 08:30:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tv39flyyfq"}, {"Name": "label", "Value": "html/termsofuse.html"}, {"Name": "integrity", "Value": "sha256-FwGOB7K0gsefWi5dVWTXT9CZFukqqkKY1DYJECdH3pY="}]}, {"Route": "html/test-auth-fix.ew7c11alxw.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\test-auth-fix.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:36:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ew7c11alxw"}, {"Name": "label", "Value": "html/test-auth-fix.html"}, {"Name": "integrity", "Value": "sha256-AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ="}]}, {"Route": "html/test-auth-fix.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\html\\test-auth-fix.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9642"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:36:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AijgHIYi/kHTRTGRxCrC8dNKMg9ABHk1p+zXtS66jXQ="}]}, {"Route": "js/teams-app.js", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20446"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SYyNHEiAHnWf6nLjhVMAGG6bB25qc3RxDo+wyenmlUs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:32:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SYyNHEiAHnWf6nLjhVMAGG6bB25qc3RxDo+wyenmlUs="}]}, {"Route": "js/teams-app.v2ht0iv8tj.js", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\js\\teams-app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20446"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"SYyNHEiAHnWf6nLjhVMAGG6bB25qc3RxDo+wyenmlUs=\""}, {"Name": "Last-Modified", "Value": "Sun, 10 Aug 2025 12:32:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v2ht0iv8tj"}, {"Name": "label", "Value": "js/teams-app.js"}, {"Name": "integrity", "Value": "sha256-SYyNHEiAHnWf6nLjhVMAGG6bB25qc3RxDo+wyenmlUs="}]}, {"Route": "teams-test.cztlu2xxj7.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cztlu2xxj7"}, {"Name": "label", "Value": "teams-test.html"}, {"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}]}, {"Route": "teams-test.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\teams-test.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6390"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 19:44:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P5zgdWxWZ2bZZMeuIi7vJMm+530R8iVicKn/7LsAgo="}]}, {"Route": "test-auth.2r3ito90zx.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2r3ito90zx"}, {"Name": "label", "Value": "test-auth.html"}, {"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}, {"Route": "test-auth.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2140"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 11:50:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fhCChm62jrYAW1g57VIivtmtjhXmOfwGW5mk+pxcI5o="}]}, {"Route": "test-teams-auth.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}]}, {"Route": "test-teams-auth.r54zswcdn2.html", "AssetFile": "D:\\iData Project\\ASR_Bot_New\\Backend\\wwwroot\\test-teams-auth.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9705"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I=\""}, {"Name": "Last-Modified", "Value": "Fri, 08 Aug 2025 20:45:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r54zswcdn2"}, {"Name": "label", "Value": "test-teams-auth.html"}, {"Name": "integrity", "Value": "sha256-zxsIQCwlwqYJYejoAnllGdxDbA0/p/akEX/n4Ydb17I="}]}]}