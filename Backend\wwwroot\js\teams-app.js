// AccureMD Teams App JavaScript

class TeamsApp {
    constructor() {
        console.log('AccureMD: TeamsApp constructor called');

        this.currentUser = null;
        this.currentMeeting = null;
        this.isRecording = false;
        this.isTranscribing = false;
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.transcripts = [];
        this.teamsContext = null;
        this.isInTeams = false; // Will be set during initialization

        console.log('AccureMD: Starting initialization...');
        this.initializeTeamsContext();
        this.setupEventListeners();
        console.log('AccureMD: Constructor completed');
    }

    detectTeamsEnvironment() {
        // Check multiple indicators to determine if we're in Teams
        try {
            const inIframe = window.self !== window.top;
            const url = window.location.href;
            const hasTeamsUrl = url.includes('teams.microsoft.com') || url.includes('teams.live.com');
            const urlParams = new URLSearchParams(window.location.search);
            const hasTeamsParams = urlParams.has('inTeams') || urlParams.has('theme');
            const userAgent = navigator.userAgent.toLowerCase();
            const hasTeamsUserAgent = userAgent.includes('teams');

            let hasTeamsParent = false;
            try {
                hasTeamsParent = inIframe && window.parent && typeof window.parent.microsoftTeams !== 'undefined';
            } catch (e) {
                hasTeamsParent = inIframe; // Cross-origin access blocked, likely in Teams
            }

            const isInTeams = hasTeamsUrl || hasTeamsParams || hasTeamsUserAgent || hasTeamsParent;
            console.log('AccureMD: Teams environment detection result:', isInTeams);
            return isInTeams;
        } catch (error) {
            console.warn('AccureMD: Error detecting Teams environment:', error);
            return false;
        }
    }

    async handleAuthenticationReturn() {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');
        const authSuccess = urlParams.get('auth');

        if (error) {
            console.error('AccureMD: Authentication error in URL:', error);
            this.showError('Authentication failed: ' + error);
            window.history.replaceState({}, document.title, window.location.pathname);
            return;
        }

        // Handle auth=success parameter from auth-callback.html redirect
        if (authSuccess === 'success') {
            console.log('AccureMD: Authentication success parameter found, cleaning URL and checking status...');
            window.history.replaceState({}, document.title, window.location.pathname);
            await this.checkAuthenticationStatus();
            return;
        }

        if (code && state) {
            console.log('AccureMD: Found authentication code in URL, processing...');
            try {
                const response = await fetch('/api/auth/callback', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ code, state })
                });

                if (response.ok) {
                    console.log('AccureMD: Authentication callback successful');
                    window.history.replaceState({}, document.title, window.location.pathname);
                    await this.checkAuthenticationStatus();
                } else {
                    throw new Error('Authentication callback failed');
                }
            } catch (err) {
                console.error('AccureMD: Error processing authentication callback:', err);
                this.showError('Authentication processing failed');
                window.history.replaceState({}, document.title, window.location.pathname);
            }
        }
    }

    async initializeTeamsContext() {
        console.log('AccureMD: Starting app initialization...');
        await this.handleAuthenticationReturn();

        if (typeof microsoftTeams === 'undefined') {
            console.log('AccureMD: Teams SDK not available, running in standalone mode.');
            this.isInTeams = false;
            this.hideLoading();
            await this.checkAuthenticationStatus();
            return;
        }

        try {
            console.log('AccureMD: Initializing Teams SDK...');
            await microsoftTeams.app.initialize();
            console.log('AccureMD: Teams SDK initialized successfully.');
            this.isInTeams = true; // Set based on successful initialization
            this.teamsContext = await microsoftTeams.app.getContext();
            console.log('AccureMD: Teams context obtained:', this.teamsContext);
        } catch (initError) {
            console.warn('AccureMD: Teams SDK initialization failed, running in browser mode:', initError);
            this.isInTeams = false;
        }

        this.hideLoading();
        await this.checkAuthenticationStatus();
    }

    setupEventListeners() {
        document.getElementById('loginBtn')?.addEventListener('click', () => this.login());
        document.getElementById('logoutBtn')?.addEventListener('click', () => this.logout());
        document.getElementById('joinBtn')?.addEventListener('click', () => this.joinMeeting());
        document.getElementById('leaveMeetingBtn')?.addEventListener('click', () => this.leaveMeeting());
        document.getElementById('startRecordingBtn')?.addEventListener('click', () => this.startRecording());
        document.getElementById('stopRecordingBtn')?.addEventListener('click', () => this.stopRecording());
    }

    async checkAuthenticationStatus() {
        try {
            let userId = 'anonymous';
            if (this.isInTeams && this.teamsContext?.user?.id) {
                userId = this.teamsContext.user.id;
                console.log('AccureMD: Got Teams user ID:', userId);
            }

            console.log('AccureMD: Checking auth status for user:', userId);
            const response = await fetch(`/api/auth/status/${userId}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const authStatus = await response.json();
            console.log('AccureMD: Auth status response:', authStatus);

            if (authStatus.isAuthenticated) {
                console.log('AccureMD: User is authenticated, showing main app.');
                this.currentUser = authStatus;
                this.showMainApp();
                this.updateUserInterface();
            } else {
                console.log('AccureMD: User is not authenticated, showing auth screen.');
                this.showAuthScreen();
            }
        } catch (error) {
            console.error('AccureMD: Error checking authentication:', error);
            this.showAuthScreen(); // Default to auth screen on error
        }
    }

    /**
     * Helper method to detect Teams desktop app
     */
    isTeamsDesktopApp() {
        const userAgent = navigator.userAgent;
        const isTeamsDesktop = userAgent.includes('Teams/');
        console.log('AccureMD: Teams desktop app detection - UserAgent:', userAgent);
        console.log('AccureMD: Is Teams desktop app:', isTeamsDesktop);
        return isTeamsDesktop;
    }

    /**
     * [REVISED] Initiates the login flow using the Teams SDK.
     */
    async login() {
        console.log('AccureMD: Starting login process with Teams SDK...');
        console.log('AccureMD: Teams desktop app detected:', this.isTeamsDesktopApp());

        // The primary method should be the Teams SDK.
        if (!this.isInTeams) {
            this.showError("This app is designed for Microsoft Teams. Authentication might not work correctly in a browser.");
            console.warn("Not in Teams environment. The legacy popup method is not recommended and likely to be blocked.");
            // You could optionally call a fallback legacy method here, but it's unreliable.
            return;
        }

        try {
            const userId = this.teamsContext?.user?.id || 'anonymous';
            const redirectUri = `${window.location.origin}/html/auth-callback.html`;

            // Use the new server-side redirect endpoint for Teams authentication
            // This endpoint performs a server-side redirect (HTTP 302) to the Microsoft login URL
            const startLoginUrl = `${window.location.origin}/api/auth/start-teams-login?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;

            console.log('AccureMD: Starting Teams authentication flow...');
            console.log('AccureMD: Using redirect URI:', redirectUri);
            console.log('AccureMD: Using start login URL:', startLoginUrl);
            console.log('AccureMD: User ID:', userId);

            // Add message listener for fallback communication
            const messageHandler = (event) => {
                if (event.data && event.data.type === 'teams-auth-complete' && event.data.success) {
                    console.log('AccureMD: Received fallback auth success message:', event.data);
                    window.removeEventListener('message', messageHandler);

                    // Process the auth data from the fallback message
                    if (event.data.data && event.data.data.accessToken) {
                        console.log('AccureMD: Processing fallback authentication data');
                        this.showNotification('Authentication successful! Updating session...', 'success');

                        // Check authentication status after a delay
                        setTimeout(async () => {
                            await this.checkAuthenticationStatus();
                        }, 1000);
                    }
                }
            };
            window.addEventListener('message', messageHandler);

            microsoftTeams.authentication.authenticate({
                url: startLoginUrl,
                width: 600,
                height: 700,
                successCallback: async (resultKey) => {
                    console.log('AccureMD: Teams auth flow succeeded. Result key:', resultKey);
                    window.removeEventListener('message', messageHandler); // Clean up fallback listener

                    try {
                        // Retrieve the authentication data from localStorage using the key
                        const authDataJson = localStorage.getItem(resultKey);
                        if (authDataJson) {
                            const authData = JSON.parse(authDataJson);
                            console.log('AccureMD: Retrieved auth data:', authData);

                            // Clean up the localStorage entry
                            localStorage.removeItem(resultKey);

                            // Store the authentication data for the app to use
                            if (authData.accessToken) {
                                console.log('AccureMD: Authentication data received successfully');
                                this.showNotification('Authentication successful! Updating session...', 'success');
                            }
                        } else {
                            console.warn('AccureMD: No auth data found for key:', resultKey);
                        }
                    } catch (error) {
                        console.error('AccureMD: Error processing auth result:', error);
                    }

                    // Add a small delay to ensure backend has processed the authentication
                    console.log('AccureMD: Waiting for backend to process authentication...');
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // After processing the auth data, check the authentication status
                    await this.checkAuthenticationStatus();
                },
                failureCallback: (reason) => {
                    // This is called if the user closes the popup or an error occurs.
                    console.error('AccureMD: Teams auth flow failed:', reason);
                    console.error('AccureMD: Failure details - URL:', startLoginUrl);
                    console.error('AccureMD: Failure details - Redirect URI:', redirectUri);
                    console.error('AccureMD: Failure details - User ID:', userId);

                    // Clean up fallback listener
                    window.removeEventListener('message', messageHandler);

                    // Special handling for CancelledByUser error in Teams desktop app
                    if (reason === 'CancelledByUser') {
                        console.log('AccureMD: CancelledByUser error detected - this might be the Teams desktop app bug');
                        console.log('AccureMD: Waiting a moment to see if authentication actually succeeded...');

                        // Wait a bit and then check if authentication actually succeeded
                        setTimeout(async () => {
                            console.log('AccureMD: Checking authentication status after CancelledByUser error...');
                            try {
                                await this.checkAuthenticationStatus();

                                // If we're still not authenticated after checking, show the error
                                if (!this.isAuthenticated) {
                                    this.showError(`Authentication failed or was cancelled: ${reason}`);
                                }
                            } catch (error) {
                                console.error('AccureMD: Error checking auth status after CancelledByUser:', error);
                                this.showError(`Authentication failed or was cancelled: ${reason}`);
                            }
                        }, 2000);
                    } else {
                        this.showError(`Authentication failed or was cancelled: ${reason}`);
                    }
                },
            });

        } catch (error) {
            console.error('AccureMD: Login process failed:', error);
            this.showError(`An error occurred during login: ${error.message}`);
        }
    }

    /**
     * [REMOVED] The openAuthPopup and its fallback logic are no longer the primary method
     * and were the source of the errors. This logic is replaced by the `microsoftTeams.authentication.authenticate` call.
     */
    // openAuthPopup(authUrl) { ... } // This function is no longer needed.

    async logout() {
        try {
            const userId = this.currentUser?.userId || 'anonymous';
            await fetch(`/api/auth/logout/${userId}`, { method: 'POST' });
            this.currentUser = null;
            this.currentMeeting = null;
            this.showAuthScreen();
        } catch (error) {
            console.error('Logout error:', error);
        }
    }

    // --- All other methods (joinMeeting, leaveMeeting, UI helpers, etc.) remain the same ---
    // ... (rest of your file)

    async joinMeeting() {
        try {
            const meetingUrl = document.getElementById('meetingUrl').value.trim();
            if (!this.validateMeetingUrl(meetingUrl)) {
                return;
            }
            this.updateConnectionStatus('Connecting...', 'connecting');
            const response = await fetch('/api/meetings/join', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    meetingUrl: meetingUrl,
                    userId: this.currentUser?.userId || 'anonymous',
                    displayName: 'AccureMD Bot'
                })
            });
            if (!response.ok) {
                throw new Error('Failed to join meeting');
            }
            const result = await response.json();
            if (result.success) {
                this.currentMeeting = { id: result.meetingId, url: meetingUrl };
                this.updateConnectionStatus('Connected to meeting', 'online');
                this.showRecordingControls();
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            this.updateConnectionStatus('Connection failed', 'offline');
            this.showError(`Error joining meeting: ${error.message}`);
        }
    }

    async leaveMeeting() {
        if (!this.currentMeeting) return;
        if (this.isRecording) await this.stopRecording();
        await fetch(`/api/meetings/${this.currentMeeting.id}/leave`, { method: 'POST' });
        this.currentMeeting = null;
        this.updateConnectionStatus('Ready to join', 'offline');
        this.hideRecordingControls();
    }

    async startRecording() {
        if (!this.currentMeeting) return;
        const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/start`, { method: 'POST' });
        const result = await response.json();
        if (result.success) {
            this.isRecording = true;
            this.updateRecordingInterface();
        } else {
            this.showError(result.message);
        }
    }

    async stopRecording() {
        if (!this.currentMeeting) return;
        const response = await fetch(`/api/meetings/${this.currentMeeting.id}/recording/stop`, { method: 'POST' });
        const result = await response.json();
        if (result.success) {
            this.isRecording = false;
            this.updateRecordingInterface();
        } else {
            this.showError(result.message);
        }
    }
    
    // --- UI Helper Methods ---
    hideLoading() { document.getElementById('loadingScreen').style.display = 'none'; }
    showAuthScreen() {
        document.getElementById('authScreen').style.display = 'flex';
        document.getElementById('mainApp').style.display = 'none';
    }
    showMainApp() {
        document.getElementById('authScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'flex';
    }
    updateUserInterface() {
        if (this.currentUser) {
            const userName = this.currentUser.userName || 'User';
            document.getElementById('userName').textContent = userName;
            document.getElementById('userAvatar').textContent = userName.charAt(0).toUpperCase();
        }
    }
    updateConnectionStatus(text, status) {
        const statusElement = document.getElementById('connectionStatus');
        statusElement.querySelector('.status-dot').className = `status-dot ${status}`;
        statusElement.querySelector('span:last-child').textContent = text;
    }
    showRecordingControls() { document.getElementById('recordingControls').style.display = 'block'; }
    hideRecordingControls() { document.getElementById('recordingControls').style.display = 'none'; }
    updateRecordingInterface() {
        const startBtn = document.getElementById('startRecordingBtn');
        const stopBtn = document.getElementById('stopRecordingBtn');
        const indicator = document.getElementById('recordingIndicator');
        if (this.isRecording) {
            startBtn.style.display = 'none';
            stopBtn.style.display = 'inline-block';
            indicator.textContent = '🔴 Recording';
        } else {
            startBtn.style.display = 'inline-block';
            stopBtn.style.display = 'none';
            indicator.textContent = '⚪ Standby';
        }
    }
    validateMeetingUrl(url) {
        if (!url || !url.startsWith('https://teams.microsoft.com/')) {
            this.showError('Please enter a valid Microsoft Teams meeting URL.');
            return false;
        }
        return true;
    }
    showError(message) { this.showNotification(message, 'error'); }
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);
        setTimeout(() => {
            notification.remove();
        }, duration);
    }
}
